import type { ToolInvocation } from '@ai-sdk/ui-utils';
import React from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import type { INodeIconValue } from '@bika/types/node/bo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';

/**
 * Helper function to get icon data for a tool and convert it to INodeIconValue
 */
export function useToolIcon(props: {
  skillsets: SkillsetSelectDTO[];
  toolInvocation: ToolInvocation;
}): INodeIconValue | undefined {
  const trpcQuery = useTRPCQuery();
  const { data: skillsetsVOs, isLoading: isLoadingSkillsetsVOs } = trpcQuery.ai.getSkillsets.useQuery(props.skillsets);

  // 从 skillsetsVOs 中获取对应的 skillsetVO, key 和 kind 相同匹配
  const skillsetVO = React.useMemo(() => {
    if (!skillsetsVOs || isLoadingSkillsetsVOs) return undefined;
    const skillset = skillsetsVOs.find((_skillset) => _skillset.key === props.toolInvocation.toolName);
    if (skillset) {
      return skillset;
    }
    return skillsetsVOs.find((_skillset) => _skillset.skills?.some((sk) => sk.key === props.toolInvocation.toolName));
  }, [skillsetsVOs, isLoadingSkillsetsVOs, props.toolInvocation.toolName]);

  // 特殊 UI 配置
  const skillsetUIMap = React.useMemo(
    () => AISkillsetClientRegistry.getManySkillsetUI(props.skillsets),
    [props.skillsets],
  );
  const skillUICfg = skillsetUIMap ? skillsetUIMap[props.toolInvocation.toolName] : undefined;

  return React.useMemo(() => {
    if (skillsetVO?.logo) {
      // 从服务端 VO 拿到图标，转换为 INodeIconValue
      return {
        kind: 'avatar' as const,
        avatar: skillsetVO.logo,
        name: skillsetVO.name,
      };
    }
    // 客户端自定义置了 skill icon (不是 skillsets 哦)，覆盖这个图标
    if (skillUICfg?.customIcon) {
      return {
        kind: 'avatar' as const,
        avatar: skillUICfg.customIcon,
      };
    }
    return undefined;
  }, [skillsetVO, skillUICfg]);
}
