'use client';

import React from 'react';
import { useApiCaller } from '@bika/api-caller';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n';
import type { INodeIconValue } from '@bika/types/node/bo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { iStringParse } from '@bika/types/system';
import { Box } from '@bika/ui/layouts';
import { getDefaultToolUIContentProps, DefaultToolRenderer, ToolErrorRenderer } from './default-tool-renderer';
import type { ToolUIProps } from './type';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';
import type { ToolUIContentProps, ToolUIComponentProps } from '../../../../ai-skillset/types';
import { useBuildInstallGlobalStore } from '../../../../space/client/intercepter/use-build-install-global-store';
import { useToolIconWithData } from '../utils/use-tool-icon';



export function ToolUI(props: ToolUIProps) {
  const localeContext = useLocale();
  const { setData: setBuildInstallData } = useBuildInstallGlobalStore();
  const spaceContext = useSpaceContextForce();

  const { useRootNode } = spaceContext || { useRootNode: () => ({ setRootNode: () => {} }) };
  const { setRootNode } = useRootNode();

  const { part, hideFlow } = props;

  // 特殊 UI 配置
  const skillsetUIMap = React.useMemo(
    () => AISkillsetClientRegistry.getManySkillsetUI(props.skillsets),
    [props.skillsets],
  );
  const skillUICfg = skillsetUIMap ? skillsetUIMap[part.toolInvocation.toolName] : undefined;

  const handleClickTool = () => {
    props.onClickTool(props.part.toolInvocation);
  };

  const defaultToolContentProps: ToolUIContentProps = getDefaultToolUIContentProps(part.toolInvocation, {
    displayName: iStringParse(skillUICfg?.displayName, localeContext.lang),
    locale: localeContext,
  });

  const apiCaller = useApiCaller();

  const doExecuteToolResult = async (toolCallId: string) => {
    if (skillUICfg?.clientExecute) {
      // 如果有 clientExecute，说明是一个客户端的 Tool，执行后，可能会有结果更新
      return skillUICfg?.clientExecute?.(part.toolInvocation, {
        apiCaller,
        setData: setBuildInstallData,
        setRootNode,
      });
    }

    // Remote Server trpc Execute Tool
    return props.executeToolResult(toolCallId);
  };

  const renderToolComponentProps: ToolUIComponentProps = {
    executeToolResult: doExecuteToolResult,
    hideFlow,
    isHighlight: props.isHighlight,
    toolInvocation: props.part.toolInvocation,
    onClickTool: handleClickTool,
    addToolResult: (userResult) => {
      props.addToolResult({ toolCallId: props.part.toolInvocation.toolCallId, result: userResult });
    },
    sendMessage: props.sendMessage,
    sendUI: props.sendUI,
    localeContext,
    skillsets: props.skillsets,
  };

  const iconValue = useToolIconWithData(props.skillsets, props.part.toolInvocation);

  if (props.error && defaultToolContentProps) {
    return (
      <Box mb={1}>
        <ToolErrorRenderer
          addToolResult={props.addToolResult}
          executeToolResult={doExecuteToolResult}
          skillsets={props.skillsets}
          localeContext={localeContext}
          contentProps={{ ...defaultToolContentProps, icon: iconValue }}
          toolInvocation={props.part.toolInvocation}
          error={props.error}
        />
      </Box>
    );
  }

  if (
    props.part.toolInvocation.state === 'result' &&
    typeof props.part.toolInvocation.result === 'object' &&
    'error' in props.part.toolInvocation.result &&
    props.part.toolInvocation.result.error
  ) {
    return (
      <Box mb={1}>
        <ToolErrorRenderer
          addToolResult={props.addToolResult}
          executeToolResult={doExecuteToolResult}
          skillsets={props.skillsets}
          localeContext={localeContext}
          contentProps={{ ...defaultToolContentProps, icon: iconValue }}
          toolInvocation={props.part.toolInvocation}
          error={props.part.toolInvocation.result.error.message}
        />
      </Box>
    );
  }

  // 出默认，比如第三方 MCP，显示 tool-invocation 的内容
  if (skillUICfg?.component === undefined) {
    // const icon = <SkillIcon skillsets={props.skillsets} toolInvocation={props.part.toolInvocation} />;
    return (
      <Box mb={1}>
        <DefaultToolRenderer
          contentProps={{ ...defaultToolContentProps, icon: iconValue }}
          {...renderToolComponentProps}
        />
      </Box>
    );
  }

  // 定制整个空间，比如，做一个生图 UI Tool
  const componentResult = skillUICfg.component(renderToolComponentProps);
  if (typeof componentResult === 'object') {
    // const icon = <SkillIcon skillsets={props.skillsets} toolInvocation={props.part.toolInvocation} />;

    //  是否是配置
    const customContentProps = {
      icon: iconValue,
      ...componentResult,
    } as ToolUIContentProps;
    return (
      <Box mb={1}>
        <DefaultToolRenderer
          contentProps={{
            ...defaultToolContentProps,
            ...customContentProps,
          }}
          {...renderToolComponentProps}
        />
      </Box>
    );
  }
  return (
    <>
      <Box mb={1}>{componentResult}</Box>
    </>
  );

  // 只修改文案，常用，比如某个 tool 的文案和icon 不同
  // const propsFunc = skillUI.component as ToolUIConfig;
  // // Partial<>，支持部分替换
  // const customContentProps: ToolUIContentProps = propsFunc(renderToolComponentProps);

  // return (
  //   <Box mb={1}>
  //     <DefaultToolRenderer
  //       contentProps={{
  //         ...defaultToolContentProps,
  //         ...customContentProps,
  //       }}
  //       {...renderToolComponentProps}
  //     />
  //   </Box>
  // );
}
